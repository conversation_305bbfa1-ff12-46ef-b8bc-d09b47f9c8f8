<?php

namespace App\Console;

use App\Console\Commands\BlockFinanceStudent;
use App\Console\Commands\FetchStudentOldSubjectCommand;
use App\Console\Commands\FillLectureGroups;
use App\Console\Commands\FinanceGraphicActiveStudent;
use App\Console\Commands\FinanceGraphicBlockStudent;
use App\Console\Commands\FinanceReportCommand;
use App\Console\Commands\FinanceStatusUpdateCommand;
use App\Console\Commands\FixDustedSurveyDBCommand;
use App\Console\Commands\InactiveStudentAttendances;
use App\Console\Commands\StudentPointCalculationCommand;
use App\Console\Commands\SurveyActivationCommand;
use App\Models\Setting;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     *
     * @param \Illuminate\Console\Scheduling\Schedule $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        $schedule->command(FinanceStatusUpdateCommand::class)->dailyAt('16:00'); // financial status updates
        $schedule->command(InactiveStudentAttendances::class)->dailyAt('22:00'); // inactive student attendances
        $schedule->command(StudentPointCalculationCommand::class)->dailyAt('23:55'); // move grades in archive and closing subjects
        $schedule->command(FixDustedSurveyDBCommand::class)->dailyAt('02:00'); //removing double survey data
        $schedule->command(FinanceReportCommand::class)->weeklyOn(5, '16:10'); //auto mail sender for lika
        //$schedule->command(SurveyActivationCommand::class)->dailyAt('23:00'); // survey activation

        // Run contract expiration check daily at 8:00 AM
        $schedule->command('contracts:check-expirations')->dailyAt('08:00');
    }
    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
