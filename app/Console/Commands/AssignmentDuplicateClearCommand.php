<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class AssignmentDuplicateClearCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'assignment:clean';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
//        2024 1 seqtemberi
        $deletedRows = DB::delete("
            DELETE sa1 FROM student_assignments sa1
            INNER JOIN student_assignments sa2
            ON sa1.student_id = sa2.student_id
            AND sa1.assignment_id = sa2.assignment_id
            AND sa1.syllabus_id = sa2.syllabus_id
            AND sa1.id > sa2.id
            WHERE sa1.created_at > '2024-09-01'
        ");

        $this->info("წაიშალა $deletedRows ჩანაწერი");
    }
}
